<?php

// Iniciar sesión si es necesario
use App\classes\Proyecto;
use App\classes\ProyectoDocContrato;
use App\classes\ProyectoDocPoliza;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en gproyecto.php.");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: lproyectos');
	exit;
}

#region region Initialize Variables
$proyecto = null;
$proyectoId = null;
$error_display = 'none';
$error_text = '';
$success_display = 'none';
$success_text = '';
#endregion Initialize Variables

#region region Session Flash Messages
if (isset($_SESSION['flash_message_success'])) {
	$success_display = 'show';
	$success_text = $_SESSION['flash_message_success'];
	unset($_SESSION['flash_message_success']);
}

if (isset($_SESSION['flash_message_error'])) {
	$error_display = 'show';
	$error_text = $_SESSION['flash_message_error'];
	unset($_SESSION['flash_message_error']);
}
#endregion Session Flash Messages

#region region Project ID Validation and Loading
// Check if project ID is provided via session (secure parameter passing)
if (isset($_SESSION['gestion_proyecto_id'])) {
	$proyectoId = filter_var($_SESSION['gestion_proyecto_id'], FILTER_VALIDATE_INT);
	
	if ($proyectoId && $proyectoId > 0) {
		try {
			$proyecto = Proyecto::get($proyectoId, $conexion);
			
			if (!$proyecto) {
				$_SESSION['flash_message_error'] = "Error: No se encontró el proyecto especificado.";
				unset($_SESSION['gestion_proyecto_id']);
				header('Location: lproyectos');
				exit;
			}
			
			// Verify project is active
			if (!$proyecto->isActivo()) {
				$_SESSION['flash_message_error'] = "Error: El proyecto especificado no está activo.";
				unset($_SESSION['gestion_proyecto_id']);
				header('Location: lproyectos');
				exit;
			}
			
		} catch (Exception $e) {
			error_log("Error loading project in gproyecto.php: " . $e->getMessage());
			$_SESSION['flash_message_error'] = "Error al cargar el proyecto: " . $e->getMessage();
			unset($_SESSION['gestion_proyecto_id']);
			header('Location: lproyectos');
			exit;
		}
	} else {
		$_SESSION['flash_message_error'] = "Error: ID de proyecto inválido.";
		unset($_SESSION['gestion_proyecto_id']);
		header('Location: lproyectos');
		exit;
	}
} else {
	// No project ID provided
	$_SESSION['flash_message_error'] = "Error: No se especificó un proyecto para gestionar.";
	header('Location: lproyectos');
	exit;
}
#endregion Project ID Validation and Loading

#region region Handle POST Actions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {

	#region region Handle Document Upload
	if (isset($_POST['action']) && $_POST['action'] == 'upload_document') {
		$document_type = $_POST['document_type'] ?? '';
		$valid_types = ['doc_levantamiento_necesidades', 'doc_cotizacion', 'doc_presentacion', 'doc_acta_inicio'];

		if (!in_array($document_type, $valid_types)) {
			$_SESSION['flash_message_error'] = 'Tipo de documento inválido.';
			header('Location: gestion-proyecto');
			exit;
		}

		$file_input_name = $document_type . '_file';

		// Check if file was uploaded without errors
		if (isset($_FILES[$file_input_name]) && $_FILES[$file_input_name]['error'] == 0) {
			$file = $_FILES[$file_input_name];

			// Validate file type
			$file_type = $file['type'];
			if ($file_type !== 'application/pdf') {
				$_SESSION['flash_message_error'] = "Error: Solo se permiten archivos PDF. Tipo detectado: {$file_type}";
			}
				// Validate file size (max 50MB)
				elseif ($file['size'] > 50 * 1024 * 1024) {
					$_SESSION['flash_message_error'] = "Error: El archivo es demasiado grande. El tamaño máximo permitido es 50MB.";
				} else {
				// Create upload directory if it doesn't exist
				$section = ($document_type === 'doc_acta_inicio') ? 'planificacion' : 'inicio';
				$upload_dir = __ROOT__ . "/resources/uploads/proyectos/{$proyectoId}/{$section}/";
				if (!file_exists($upload_dir)) {
					if (!mkdir($upload_dir, 0755, true)) {
						$_SESSION['flash_message_error'] = "Error: No se pudo crear el directorio de destino para el archivo.";
						header('Location: gestion-proyecto');
						exit;
					}
				}

				// Ensure directory is writable
				if (!is_writable($upload_dir)) {
					$_SESSION['flash_message_error'] = "Error: El directorio de destino no tiene permisos de escritura.";
					header('Location: gestion-proyecto');
					exit;
				}

				// Generate filename with timestamp
				$timestamp = date('YmdHis'); // Format: yyyyMMddhhmmss
				$filename = "{$document_type}_{$timestamp}.pdf";
				$target_file = $upload_dir . $filename;

				// Move uploaded file to target location
				if (move_uploaded_file($file['tmp_name'], $target_file)) {
					// Update project document field
					try {
						// Set the appropriate document field
						switch ($document_type) {
							case 'doc_levantamiento_necesidades':
								$proyecto->setDocLevantamientoNecesidades($filename);
								break;
							case 'doc_cotizacion':
								$proyecto->setDocCotizacion($filename);
								break;
							case 'doc_presentacion':
								$proyecto->setDocPresentacion($filename);
								break;
							case 'doc_acta_inicio':
								$proyecto->setDocActaInicio($filename);
								break;
						}

						$success = $proyecto->actualizarDocumentos($conexion);

						if ($success) {
							$document_names = [
								'doc_levantamiento_necesidades' => 'Levantamiento de Necesidades',
								'doc_cotizacion' => 'Cotización',
								'doc_presentacion' => 'Presentación'
							];
							$_SESSION['flash_message_success'] = "El documento '{$document_names[$document_type]}' ha sido subido correctamente.";
						} else {
							$_SESSION['flash_message_error'] = "Error: El archivo se subió pero no se pudo actualizar la base de datos.";
						}
					} catch (Exception $e) {
						$_SESSION['flash_message_error'] = "Error al actualizar el documento: " . $e->getMessage();
					}
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo mover el archivo subido a la ubicación final.";
				}
			}
		} else {
			$error_code = $_FILES[$file_input_name]['error'] ?? 'desconocido';
			$_SESSION['flash_message_error'] = "Error al subir el archivo (código {$error_code}). Por favor, inténtelo de nuevo.";
		}

		header('Location: gestion-proyecto');
		exit;
	}
	#endregion Handle Document Upload

	#region region Handle Contrato Upload
	if (isset($_POST['action']) && $_POST['action'] == 'upload_contrato') {
		$id_proyecto = filter_var($_POST['id_proyecto'] ?? 0, FILTER_VALIDATE_INT);

		if (!$id_proyecto || $id_proyecto !== $proyectoId) {
			$_SESSION['flash_message_error'] = 'ID de proyecto inválido.';
			header('Location: gestion-proyecto');
			exit;
		}

		// Check if file was uploaded without errors
		if (isset($_FILES['contrato_file']) && $_FILES['contrato_file']['error'] == 0) {
			$file = $_FILES['contrato_file'];

			// Validate file type
			$file_type = $file['type'];
			if ($file_type !== 'application/pdf') {
				$_SESSION['flash_message_error'] = "Error: Solo se permiten archivos PDF. Tipo detectado: {$file_type}";
			}
			// Validate file size (max 50MB)
			elseif ($file['size'] > 50 * 1024 * 1024) {
				$_SESSION['flash_message_error'] = "Error: El archivo es demasiado grande. El tamaño máximo permitido es 50MB.";
			} else {
				// Create upload directory if it doesn't exist
				$upload_dir = __ROOT__ . "/resources/uploads/proyectos/{$proyectoId}/planificacion/";
				if (!file_exists($upload_dir)) {
					if (!mkdir($upload_dir, 0755, true)) {
						$_SESSION['flash_message_error'] = "Error: No se pudo crear el directorio de destino para el archivo.";
						header('Location: gestion-proyecto');
						exit;
					}
				}

				// Ensure directory is writable
				if (!is_writable($upload_dir)) {
					$_SESSION['flash_message_error'] = "Error: El directorio de destino no tiene permisos de escritura.";
					header('Location: gestion-proyecto');
					exit;
				}

				// Generate filename with timestamp
				$timestamp = date('YmdHis'); // Format: yyyyMMddhhmmss
				$filename = "contrato_{$timestamp}.pdf";
				$target_file = $upload_dir . $filename;

				// Move uploaded file to target location
				if (move_uploaded_file($file['tmp_name'], $target_file)) {
					// Create new ProyectoDocContrato object and save to database
					try {
						$contrato = new ProyectoDocContrato();
						$contrato->setIdProyecto($proyectoId);
						$contrato->setArchivo($filename);

						$contrato_id = $contrato->crear($conexion);

						if ($contrato_id) {
							$_SESSION['flash_message_success'] = "El contrato ha sido subido correctamente.";
						} else {
							$_SESSION['flash_message_error'] = "Error: El archivo se subió pero no se pudo guardar en la base de datos.";
						}
					} catch (Exception $e) {
						$_SESSION['flash_message_error'] = "Error al guardar el contrato: " . $e->getMessage();
					}
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo mover el archivo subido a la ubicación final.";
				}
			}
		} else {
			$error_code = $_FILES['contrato_file']['error'] ?? 'desconocido';
			$_SESSION['flash_message_error'] = "Error al subir el archivo (código {$error_code}). Por favor, inténtelo de nuevo.";
		}

		header('Location: gestion-proyecto');
		exit;
	}
	#endregion Handle Contrato Upload

	#region region Handle Poliza Upload
	if (isset($_POST['action']) && $_POST['action'] == 'upload_poliza') {
		$id_proyecto = filter_var($_POST['id_proyecto'] ?? 0, FILTER_VALIDATE_INT);

		if (!$id_proyecto || $id_proyecto !== $proyectoId) {
			$_SESSION['flash_message_error'] = 'ID de proyecto inválido.';
			header('Location: gestion-proyecto');
			exit;
		}

		// Check if file was uploaded without errors
		if (isset($_FILES['poliza_file']) && $_FILES['poliza_file']['error'] == 0) {
			$file = $_FILES['poliza_file'];

			// Validate file type
			$file_type = $file['type'];
			if ($file_type !== 'application/pdf') {
				$_SESSION['flash_message_error'] = "Error: Solo se permiten archivos PDF. Tipo detectado: {$file_type}";
			}
			// Validate file size (max 50MB)
			elseif ($file['size'] > 50 * 1024 * 1024) {
				$_SESSION['flash_message_error'] = "Error: El archivo es demasiado grande. El tamaño máximo permitido es 50MB.";
			} else {
				// Create upload directory if it doesn't exist
				$upload_dir = __ROOT__ . "/resources/uploads/proyectos/{$proyectoId}/planificacion/";
				if (!file_exists($upload_dir)) {
					if (!mkdir($upload_dir, 0755, true)) {
						$_SESSION['flash_message_error'] = "Error: No se pudo crear el directorio de destino para el archivo.";
						header('Location: gestion-proyecto');
						exit;
					}
				}

				// Ensure directory is writable
				if (!is_writable($upload_dir)) {
					$_SESSION['flash_message_error'] = "Error: El directorio de destino no tiene permisos de escritura.";
					header('Location: gestion-proyecto');
					exit;
				}

				// Generate filename with timestamp
				$timestamp = date('YmdHis'); // Format: yyyyMMddhhmmss
				$filename = "poliza_{$timestamp}.pdf";
				$target_file = $upload_dir . $filename;

				// Move uploaded file to target location
				if (move_uploaded_file($file['tmp_name'], $target_file)) {
					// Create new ProyectoDocPoliza object and save to database
					try {
						$poliza = new ProyectoDocPoliza();
						$poliza->setIdProyecto($proyectoId);
						$poliza->setArchivo($filename);

						$poliza_id = $poliza->crear($conexion);

						if ($poliza_id) {
							$_SESSION['flash_message_success'] = "La póliza ha sido subida correctamente.";
						} else {
							$_SESSION['flash_message_error'] = "Error: El archivo se subió pero no se pudo guardar en la base de datos.";
						}
					} catch (Exception $e) {
						$_SESSION['flash_message_error'] = "Error al guardar la póliza: " . $e->getMessage();
					}
				} else {
					$_SESSION['flash_message_error'] = "Error: No se pudo mover el archivo subido a la ubicación final.";
				}
			}
		} else {
			$error_code = $_FILES['poliza_file']['error'] ?? 'desconocido';
			$_SESSION['flash_message_error'] = "Error al subir el archivo (código {$error_code}). Por favor, inténtelo de nuevo.";
		}

		header('Location: gestion-proyecto');
		exit;
	}
	#endregion Handle Poliza Upload

	#region region Handle Delete Contrato
	if (isset($_POST['action']) && $_POST['action'] == 'delete_contrato') {
		$contrato_id = filter_var($_POST['contrato_id'] ?? 0, FILTER_VALIDATE_INT);

		if (!$contrato_id) {
			echo json_encode(['success' => false, 'message' => 'ID de contrato inválido.']);
			exit;
		}

		try {
			$contrato = ProyectoDocContrato::get($contrato_id, $conexion);

			if (!$contrato) {
				echo json_encode(['success' => false, 'message' => 'Contrato no encontrado.']);
				exit;
			}

			// Verify the contract belongs to the current project
			if ($contrato->getIdProyecto() !== $proyectoId) {
				echo json_encode(['success' => false, 'message' => 'No tiene permisos para eliminar este contrato.']);
				exit;
			}

			$success = $contrato->eliminar($conexion);

			if ($success) {
				echo json_encode(['success' => true, 'message' => 'Contrato eliminado correctamente.']);
			} else {
				echo json_encode(['success' => false, 'message' => 'Error al eliminar el contrato.']);
			}
		} catch (Exception $e) {
			echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
		}
		exit;
	}
	#endregion Handle Delete Contrato

	#region region Handle Delete Poliza
	if (isset($_POST['action']) && $_POST['action'] == 'delete_poliza') {
		$poliza_id = filter_var($_POST['poliza_id'] ?? 0, FILTER_VALIDATE_INT);

		if (!$poliza_id) {
			echo json_encode(['success' => false, 'message' => 'ID de póliza inválido.']);
			exit;
		}

		try {
			$poliza = ProyectoDocPoliza::get($poliza_id, $conexion);

			if (!$poliza) {
				echo json_encode(['success' => false, 'message' => 'Póliza no encontrada.']);
				exit;
			}

			// Verify the policy belongs to the current project
			if ($poliza->getIdProyecto() !== $proyectoId) {
				echo json_encode(['success' => false, 'message' => 'No tiene permisos para eliminar esta póliza.']);
				exit;
			}

			$success = $poliza->eliminar($conexion);

			if ($success) {
				echo json_encode(['success' => true, 'message' => 'Póliza eliminada correctamente.']);
			} else {
				echo json_encode(['success' => false, 'message' => 'Error al eliminar la póliza.']);
			}
		} catch (Exception $e) {
			echo json_encode(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
		}
		exit;
	}
	#endregion Handle Delete Poliza

	// Handle any other POST actions here in the future
	header('Location: gestion-proyecto');
	exit;
}
#endregion Handle POST Actions

#region region Load Documents for View
$contratos = [];
$polizas = [];

if ($proyecto) {
	try {
		// Load contracts for this project
		$contratos = ProyectoDocContrato::getByProyecto($proyectoId, $conexion);

		// Load policies for this project
		$polizas = ProyectoDocPoliza::getByProyecto($proyectoId, $conexion);

	} catch (Exception $e) {
		error_log("Error loading documents for project {$proyectoId}: " . $e->getMessage());
		// Continue with empty arrays if there's an error
	}
}
#endregion Load Documents for View

// Load the view
require_once __ROOT__ . '/views/admin/gproyecto.view.php';

?>
