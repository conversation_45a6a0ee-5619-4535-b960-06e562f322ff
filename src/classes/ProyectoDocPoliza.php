<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class ProyectoDocPoliza
{
    // --- Atributos ---
    private ?int    $id             = null;
    private ?int    $id_proyecto    = null;
    private ?string $nombre_proyecto = null; // Added attribute to avoid redundant lookups
    private ?string $archivo        = null;
    private ?string $fecha_creacion = null;

    /**
     * Constructor: Inicializa las propiedades del objeto ProyectoDocPoliza.
     */
    public function __construct()
    {
        $this->id             = null;
        $this->id_proyecto    = null;
        $this->nombre_proyecto = null;
        $this->archivo        = null;
        $this->fecha_creacion = null;
    }

    /**
     * Método estático para construir un objeto ProyectoDocPoliza desde un array (ej. fila de DB).
     *
     * @param array $resultado Array asociativo con los datos del documento póliza.
     *
     * @return self Instancia de ProyectoDocPoliza.
     * @throws Exception Si ocurre un error durante la construcción.
     */
    public static function construct(array $resultado = []): self
    {
        try {
            $objeto                  = new self();
            $objeto->id              = isset($resultado['id']) ? (int)$resultado['id'] : null;
            $objeto->id_proyecto     = isset($resultado['id_proyecto']) ? (int)$resultado['id_proyecto'] : null;
            $objeto->nombre_proyecto = $resultado['nombre_proyecto'] ?? null;
            $objeto->archivo         = $resultado['archivo'] ?? null;
            $objeto->fecha_creacion  = $resultado['fecha_creacion'] ?? null;
            return $objeto;
        } catch (Exception $e) {
            throw new Exception("Error al construir ProyectoDocPoliza: " . $e->getMessage());
        }
    }

    // --- Métodos de Acceso a Datos (Estáticos) ---

    /**
     * Obtiene un documento póliza por su ID.
     *
     * @param int $id       ID del documento póliza.
     * @param PDO $conexion Conexión PDO.
     *
     * @return self|null Objeto ProyectoDocPoliza o null si no se encuentra.
     * @throws Exception Si hay error en DB.
     */
    public static function get(int $id, PDO $conexion): ?self
    {
        try {
            $query = <<<SQL
            SELECT
                pdp.*,
                p.descripcion as nombre_proyecto
            FROM proyectos_docs_polizas pdp
            LEFT JOIN proyectos p ON pdp.id_proyecto = p.id
            WHERE
                pdp.id = :id
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id", $id, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? self::construct($resultado) : null;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener ProyectoDocPoliza (ID: $id): " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de documentos pólizas por proyecto.
     *
     * @param int $id_proyecto ID del proyecto.
     * @param PDO $conexion    Conexión PDO.
     *
     * @return array Array de objetos ProyectoDocPoliza.
     * @throws Exception Si hay error en DB.
     */
    public static function getByProyecto(int $id_proyecto, PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                pdp.*,
                p.descripcion as nombre_proyecto
            FROM proyectos_docs_polizas pdp
            LEFT JOIN proyectos p ON pdp.id_proyecto = p.id
            WHERE
                pdp.id_proyecto = :id_proyecto
            ORDER BY
                pdp.fecha_creacion DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_proyecto', $id_proyecto, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de ProyectoDocPoliza por proyecto: " . $e->getMessage());
        }
    }

    /**
     * Obtiene una lista de todos los documentos pólizas.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return array Array de objetos ProyectoDocPoliza.
     * @throws Exception Si hay error en DB.
     */
    public static function get_list(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT
                pdp.*,
                p.descripcion as nombre_proyecto
            FROM proyectos_docs_polizas pdp
            LEFT JOIN proyectos p ON pdp.id_proyecto = p.id
            ORDER BY
                pdp.fecha_creacion DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            foreach ($resultados as $resultado) {
                $listado[] = self::construct($resultado);
            }
            return $listado;

        } catch (PDOException $e) {
            throw new Exception("Error al obtener lista de ProyectoDocPoliza: " . $e->getMessage());
        }
    }

    /**
     * Crea un nuevo documento póliza en la base de datos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo documento póliza creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    function crear(PDO $conexion): int|false
    {
        // Validaciones básicas sobre el objeto
        if (empty($this->getIdProyecto()) || empty($this->getArchivo())) {
            throw new Exception("ID del proyecto y archivo son requeridos para crear el documento póliza.");
        }

        // Validar que el proyecto existe
        if (!$this->proyectoExiste($this->getIdProyecto(), $conexion)) {
            throw new Exception("Error al crear documento póliza: El proyecto especificado no existe.");
        }

        try {
            date_default_timezone_set('America/Bogota');

            $query = <<<SQL
            INSERT INTO proyectos_docs_polizas (
                 id_proyecto
                ,archivo
            ) VALUES (
                 :id_proyecto
                ,:archivo
            )
            SQL;

            $statement = $conexion->prepare($query);

            // Bind de parámetros desde el objeto
            $statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
            $statement->bindValue(':archivo', $this->getArchivo(), PDO::PARAM_STR);

            // Ejecutar la consulta
            $success = $statement->execute();

            if ($success) {
                // Devolver el ID del documento póliza recién creado
                return (int)$conexion->lastInsertId();
            } else {
                return false; // Error en la ejecución
            }

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al crear documento póliza: " . $e->getMessage());
        } catch (Exception $e) {
            throw new Exception("Error al crear documento póliza: " . $e->getMessage());
        }
    }

    /**
     * Modifica los datos de un documento póliza existente.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la modificación fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o si ocurre un error de base de datos.
     */
    function modificar(PDO $conexion): bool
    {
        if (empty($this->getId()) || $this->getId() <= 0) {
            throw new Exception("Se requiere un ID válido para modificar el documento póliza.");
        }

        if (empty($this->getIdProyecto()) || empty($this->getArchivo())) {
            throw new Exception("ID del proyecto y archivo son requeridos para modificar el documento póliza.");
        }

        // Validar que el proyecto existe
        if (!$this->proyectoExiste($this->getIdProyecto(), $conexion)) {
            throw new Exception("Error al modificar documento póliza: El proyecto especificado no existe.");
        }

        try {
            date_default_timezone_set('America/Bogota');

            $query = <<<SQL
            UPDATE proyectos_docs_polizas SET
                id_proyecto = :id_proyecto,
                archivo = :archivo
            WHERE
                id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
            $statement->bindValue(':archivo', $this->getArchivo(), PDO::PARAM_STR);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al modificar documento póliza (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Inserta un nuevo documento póliza en la base de datos. (Método privado)
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return int|false El ID del nuevo documento póliza creado o false en caso de error.
     * @throws Exception Si los datos requeridos en el objeto están vacíos o hay error en DB.
     */
    private function _insert(PDO $conexion): int|false
    {
        return $this->crear($conexion);
    }

    /**
     * Actualiza un documento póliza existente en la base de datos. (Método privado)
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la actualización fue exitosa, False en caso contrario.
     * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
     */
    private function _update(PDO $conexion): bool
    {
        return $this->modificar($conexion);
    }

    /**
     * Elimina un documento póliza de la base de datos y del sistema de archivos.
     *
     * @param PDO $conexion Conexión PDO.
     *
     * @return bool True si la eliminación fue exitosa, False en caso contrario.
     * @throws Exception Si el ID es inválido o hay error en DB.
     */
    function eliminar(PDO $conexion): bool
    {
        if (empty($this->getId()) || $this->getId() <= 0) {
            throw new Exception("Se requiere un ID válido para eliminar el documento póliza.");
        }

        try {
            // Get file path before deletion for cleanup
            $archivo = $this->getArchivo();
            $id_proyecto = $this->getIdProyecto();

            // Delete from database
            $query = <<<SQL
            DELETE FROM proyectos_docs_polizas
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
            $success = $statement->execute();

            // If database deletion was successful, try to delete the file
            if ($success && $archivo && $id_proyecto) {
                $file_path = __ROOT__ . "/resources/uploads/proyectos/{$id_proyecto}/planificacion/{$archivo}";
                if (file_exists($file_path)) {
                    @unlink($file_path); // Use @ to suppress warnings if file doesn't exist
                }
            }

            return $success;

        } catch (PDOException $e) {
            throw new Exception("Error de base de datos al eliminar documento póliza (ID: {$this->getId()}): " . $e->getMessage());
        }
    }

    /**
     * Verifica si un proyecto existe en la base de datos.
     *
     * @param int $id_proyecto ID del proyecto a verificar.
     * @param PDO $conexion    Conexión PDO.
     *
     * @return bool True si el proyecto existe, False en caso contrario.
     * @throws Exception Si hay error en DB.
     */
    private function proyectoExiste(int $id_proyecto, PDO $conexion): bool
    {
        try {
            $query = <<<SQL
            SELECT COUNT(*) as count
            FROM proyectos
            WHERE id = :id_proyecto
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id_proyecto', $id_proyecto, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return (int)$resultado['count'] > 0;

        } catch (PDOException $e) {
            throw new Exception("Error al verificar existencia del proyecto: " . $e->getMessage());
        }
    }

    // --- Getters y Setters ---

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getIdProyecto(): ?int
    {
        return $this->id_proyecto;
    }

    public function setIdProyecto(?int $id_proyecto): self
    {
        $this->id_proyecto = $id_proyecto;
        return $this;
    }

    public function getNombreProyecto(): ?string
    {
        return $this->nombre_proyecto;
    }

    public function setNombreProyecto(?string $nombre_proyecto): self
    {
        $this->nombre_proyecto = $nombre_proyecto;
        return $this;
    }

    public function getArchivo(): ?string
    {
        return $this->archivo;
    }

    public function setArchivo(?string $archivo): self
    {
        $this->archivo = $archivo;
        return $this;
    }

    public function getFechaCreacion(): ?string
    {
        return $this->fecha_creacion;
    }

    public function setFechaCreacion(?string $fecha_creacion): self
    {
        $this->fecha_creacion = $fecha_creacion;
        return $this;
    }

    /**
     * Formatea la fecha de creación en formato yyyy-MM-dd.
     *
     * @return string|null Fecha formateada o null si no hay fecha.
     */
    public function getFechaCreacionFormateada(): ?string
    {
        if ($this->fecha_creacion === null) {
            return null;
        }

        try {
            date_default_timezone_set('America/Bogota');
            $fecha = new \DateTime($this->fecha_creacion);
            return $fecha->format('Y-m-d');
        } catch (Exception $e) {
            return $this->fecha_creacion; // Devolver original si hay error en formato
        }
    }
}
