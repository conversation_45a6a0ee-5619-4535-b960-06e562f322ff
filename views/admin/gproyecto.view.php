<?php
#region region DOCS

/** @var Proyecto $proyecto */
/** @var int $proyectoId */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */
/** @var App\classes\ProyectoDocContrato[] $contratos */
/** @var App\classes\ProyectoDocPoliza[] $polizas */

use App\classes\Proyecto;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Gestión de Proyecto</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Gestión de proyecto" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>

<body>
<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Gestión de Proyecto</h4>
				<p class="mb-0 text-muted">
					<?php if ($proyecto): ?>
						<?php echo htmlspecialchars($proyecto->getDescripcion()); ?>
					<?php else: ?>
						Administra los parámetros del proyecto
					<?php endif; ?>
				</p>
			</div>
			<div class="ms-auto">
				<a href="lproyectos" class="btn btn-secondary">
					<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a Proyectos
				</a>
			</div>
		</div>
		<hr>
		<?php #endregion PAGE HEADER ?>

		<?php if ($proyecto): ?>
		<?php #region region PROJECT MANAGEMENT CONTENT ?>
		<div class="row">
			<!-- Left Sidebar with Vertical Tabs -->
			<div class="col-md-3">
				<div class="panel panel-inverse no-border-radious">
					<div class="panel-heading no-border-radious">
						<h4 class="panel-title">Navegación</h4>
					</div>
					<div class="panel-body p-0">
						<div class="nav flex-column nav-pills" id="v-pills-tab" role="tablist" aria-orientation="vertical">
							<button class="nav-link active text-start" id="v-pills-informacion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-informacion" type="button" role="tab"
									aria-controls="v-pills-informacion" aria-selected="true">
								<i class="fa fa-info-circle fa-fw me-2"></i> Información
							</button>
							<button class="nav-link text-start" id="v-pills-inicio-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-inicio" type="button" role="tab"
									aria-controls="v-pills-inicio" aria-selected="false">
								<i class="fa fa-home fa-fw me-2"></i> Inicio
							</button>
							<button class="nav-link text-start" id="v-pills-planificacion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-planificacion" type="button" role="tab"
									aria-controls="v-pills-planificacion" aria-selected="false">
								<i class="fa fa-calendar-alt fa-fw me-2"></i> Planificación
							</button>
							<button class="nav-link text-start" id="v-pills-ejecucion-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-ejecucion" type="button" role="tab"
									aria-controls="v-pills-ejecucion" aria-selected="false">
								<i class="fa fa-play-circle fa-fw me-2"></i> Ejecución
							</button>
							<button class="nav-link text-start" id="v-pills-monitoreo-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-monitoreo" type="button" role="tab"
									aria-controls="v-pills-monitoreo" aria-selected="false">
								<i class="fa fa-chart-line fa-fw me-2"></i> Monitoreo y Control
							</button>
							<button class="nav-link text-start" id="v-pills-cierre-tab" data-bs-toggle="pill"
									data-bs-target="#v-pills-cierre" type="button" role="tab"
									aria-controls="v-pills-cierre" aria-selected="false">
								<i class="fa fa-flag-checkered fa-fw me-2"></i> Cierre
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Main Content Area -->
			<div class="col-md-9">
				<div class="tab-content" id="v-pills-tabContent">

					<?php #region region TAB: INFORMACIÓN ?>
					<div class="tab-pane fade show active" id="v-pills-informacion" role="tabpanel"
						 aria-labelledby="v-pills-informacion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-info-circle fa-fw me-2"></i> Información del Proyecto
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">ID del Proyecto:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getId()); ?></p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Descripción:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getDescripcion()); ?></p>
										</div>
									</div>
									<div class="col-md-6">
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Creación:</label>
											<p class="form-control-plaintext">
												<?php
												$fechaCreacion = $proyecto->getFechaCreacion();
												if ($fechaCreacion) {
													// Format date to show only yyyy-MM-dd portion
													$fecha = date('Y-m-d', strtotime($fechaCreacion));
													echo htmlspecialchars($fecha);
												} else {
													echo 'N/A';
												}
												?>
											</p>
										</div>
										<div class="mb-3">
											<label class="form-label fw-bold">Fecha de Inicio:</label>
											<p class="form-control-plaintext"><?php echo htmlspecialchars($proyecto->getFechaInicio() ?? 'N/A'); ?></p>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-md-12">
										<div class="mb-3">
											<label class="form-label fw-bold">Estado:</label>
											<p class="form-control-plaintext">
												<?php if ($proyecto->isActivo()): ?>
													<span class="badge bg-success">Activo</span>
												<?php else: ?>
													<span class="badge bg-danger">Inactivo</span>
												<?php endif; ?>
											</p>
										</div>
									</div>
								</div>
								
								<div class="mt-4">
									<h5><i class="fa fa-cogs fa-fw me-2"></i> Acciones Rápidas</h5>
									<div class="d-flex gap-2 flex-wrap">
										<a href="eproyecto?id=<?php echo $proyecto->getId(); ?>" class="btn btn-primary btn-sm">
											<i class="fa fa-edit fa-fw me-1"></i> Editar Proyecto
										</a>
										<a href="lproyectos?action=ver_tareas&proyecto_id=<?php echo $proyecto->getId(); ?>" class="btn btn-info btn-sm">
											<i class="fa fa-tasks fa-fw me-1"></i> Ver Tareas
										</a>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: INFORMACIÓN ?>

					<?php #region region TAB: INICIO ?>
					<div class="tab-pane fade" id="v-pills-inicio" role="tabpanel"
						 aria-labelledby="v-pills-inicio-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-home fa-fw me-2"></i> Documentos de Inicio
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-12">
										<p class="text-muted mb-4">
											<i class="fa fa-info-circle me-1"></i>
											Sube los documentos iniciales del proyecto. Los archivos son opcionales y pueden ser reemplazados en cualquier momento.
										</p>
									</div>
								</div>

								<div class="row">
									<!-- Levantamiento de Necesidades -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-primary text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-clipboard-list me-2"></i>
													Levantamiento de Necesidades
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocLevantamientoNecesidades()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocLevantamientoNecesidades()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocLevantamientoNecesidades()); ?>"
														   class="btn btn-sm btn-outline-primary" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_levantamiento_necesidades">
						<input type="hidden" name="active_tab" value="v-pills-inicio" class="dynamic-tab-input">

													<div class="mb-3">
     									<label for="doc_levantamiento_necesidades_file" class="form-label">
     										<?php echo $proyecto->getDocLevantamientoNecesidades() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_levantamiento_necesidades_file"
     									   name="doc_levantamiento_necesidades_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-primary btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocLevantamientoNecesidades() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>

									<!-- Cotización -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-success text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-dollar-sign me-2"></i>
													Cotización
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocCotizacion()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocCotizacion()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocCotizacion()); ?>"
														   class="btn btn-sm btn-outline-success" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_cotizacion">
						<input type="hidden" name="active_tab" value="v-pills-inicio" class="dynamic-tab-input">

													<div class="mb-3">
     									<label for="doc_cotizacion_file" class="form-label">
     										<?php echo $proyecto->getDocCotizacion() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_cotizacion_file"
     									   name="doc_cotizacion_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-success btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocCotizacion() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>

									<!-- Presentación -->
									<div class="col-md-4 mb-4">
										<div class="card h-100">
											<div class="card-header bg-info text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-presentation me-2"></i>
													Presentación
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocPresentacion()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocPresentacion()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/inicio/<?php echo htmlspecialchars($proyecto->getDocPresentacion()); ?>"
														   class="btn btn-sm btn-outline-info" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_presentacion">
						<input type="hidden" name="active_tab" value="v-pills-inicio" class="dynamic-tab-input">

													<div class="mb-3">
     									<label for="doc_presentacion_file" class="form-label">
     										<?php echo $proyecto->getDocPresentacion() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
     									</label>
     									<input type="file" class="form-control"
     									   id="doc_presentacion_file"
     									   name="doc_presentacion_file"
     									   accept="application/pdf">
     									<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-info btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocPresentacion() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: INICIO ?>

					<?php #region region TAB: PLANIFICACIÓN ?>
					<div class="tab-pane fade" id="v-pills-planificacion" role="tabpanel"
						 aria-labelledby="v-pills-planificacion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-calendar-alt fa-fw me-2"></i> Planificación
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="row">
									<div class="col-md-12">
										<p class="text-muted mb-4">
											<i class="fa fa-info-circle me-1"></i>
											Sube los documentos de planificación del proyecto. Los archivos son opcionales y pueden ser reemplazados en cualquier momento.
										</p>
									</div>
								</div>

								<div class="row">
									<!-- Acta de Inicio - Full Width -->
									<div class="col-md-12 mb-4">
										<div class="card h-100">
											<div class="card-header bg-warning text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-file-contract me-2"></i>
													Acta de Inicio
												</h6>
											</div>
											<div class="card-body">
												<?php if ($proyecto->getDocActaInicio()): ?>
													<div class="mb-3">
														<p class="text-success mb-2">
															<i class="fa fa-check-circle me-1"></i>
															Archivo actual: <?php echo htmlspecialchars($proyecto->getDocActaInicio()); ?>
														</p>
														<a href="../resources/uploads/proyectos/<?php echo $proyecto->getId(); ?>/planificacion/<?php echo htmlspecialchars($proyecto->getDocActaInicio()); ?>"
														   class="btn btn-sm btn-outline-warning" target="_blank">
															<i class="fa fa-download me-1"></i> Descargar
														</a>
													</div>
													<hr>
												<?php endif; ?>

												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form">
													<input type="hidden" name="action" value="upload_document">
													<input type="hidden" name="document_type" value="doc_acta_inicio">
						<input type="hidden" name="active_tab" value="v-pills-planificacion" class="dynamic-tab-input">

													<div class="mb-3">
														<label for="doc_acta_inicio_file" class="form-label">
															<?php echo $proyecto->getDocActaInicio() ? 'Reemplazar archivo' : 'Subir archivo'; ?>
														</label>
														<input type="file" class="form-control"
															   id="doc_acta_inicio_file"
															   name="doc_acta_inicio_file"
															   accept="application/pdf">
														<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-warning btn-sm w-100">
														<i class="fa fa-upload me-1"></i>
														<?php echo $proyecto->getDocActaInicio() ? 'Reemplazar' : 'Subir'; ?>
													</button>
												</form>
											</div>
										</div>
									</div>
								</div>

								<!-- Contratos y Pólizas Section -->
								<div class="row">
									<!-- Contratos Section -->
									<div class="col-md-6 mb-4">
										<div class="card h-100">
											<div class="card-header bg-primary text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-file-contract me-2"></i>
													Contratos
												</h6>
											</div>
											<div class="card-body">
												<!-- Upload Form -->
												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form mb-3">
													<input type="hidden" name="action" value="upload_contrato">
													<input type="hidden" name="id_proyecto" value="<?php echo $proyecto->getId(); ?>">
													<input type="hidden" name="active_tab" value="v-pills-planificacion" class="dynamic-tab-input">

													<div class="mb-3">
														<label for="contrato_file" class="form-label">Subir Contrato</label>
														<input type="file" class="form-control"
															   id="contrato_file"
															   name="contrato_file"
															   accept="application/pdf">
														<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-primary btn-xs w-100">
														<i class="fa fa-upload me-1"></i> Subir Contrato
													</button>
												</form>

												<!-- Contratos Table -->
												<div id="contratos-table-container">
													<div class="table-responsive">
														<table class="table table-sm table-striped" id="contratos-table">
															<thead>
																<tr>
																	<th>Archivo</th>
																	<th>Fecha</th>
																	<th>Acciones</th>
																</tr>
															</thead>
															<tbody id="contratos-table-body">
																<!-- Contratos will be loaded here -->
															</tbody>
														</table>
													</div>
												</div>
											</div>
										</div>
									</div>

									<!-- Pólizas Section -->
									<div class="col-md-6 mb-4">
										<div class="card h-100">
											<div class="card-header bg-success text-white">
												<h6 class="card-title mb-0">
													<i class="fa fa-shield-alt me-2"></i>
													Pólizas
												</h6>
											</div>
											<div class="card-body">
												<!-- Upload Form -->
												<form method="POST" action="gestion-proyecto" enctype="multipart/form-data" class="document-upload-form mb-3">
													<input type="hidden" name="action" value="upload_poliza">
													<input type="hidden" name="id_proyecto" value="<?php echo $proyecto->getId(); ?>">
													<input type="hidden" name="active_tab" value="v-pills-planificacion" class="dynamic-tab-input">

													<div class="mb-3">
														<label for="poliza_file" class="form-label">Subir Póliza</label>
														<input type="file" class="form-control"
															   id="poliza_file"
															   name="poliza_file"
															   accept="application/pdf">
														<div class="form-text">Solo archivos PDF (máx. 50MB)</div>
													</div>

													<button type="submit" class="btn btn-success btn-xs w-100">
														<i class="fa fa-upload me-1"></i> Subir Póliza
													</button>
												</form>

												<!-- Pólizas Table -->
												<div id="polizas-table-container">
													<div class="table-responsive">
														<table class="table table-sm table-striped" id="polizas-table">
															<thead>
																<tr>
																	<th>Archivo</th>
																	<th>Fecha</th>
																	<th>Acciones</th>
																</tr>
															</thead>
															<tbody id="polizas-table-body">
																<!-- Pólizas will be loaded here -->
															</tbody>
														</table>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: PLANIFICACIÓN ?>

					<?php #region region TAB: EJECUCIÓN ?>
					<div class="tab-pane fade" id="v-pills-ejecucion" role="tabpanel"
						 aria-labelledby="v-pills-ejecucion-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-play-circle fa-fw me-2"></i> Ejecución
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Ejecución - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: EJECUCIÓN ?>

					<?php #region region TAB: MONITOREO Y CONTROL ?>
					<div class="tab-pane fade" id="v-pills-monitoreo" role="tabpanel"
						 aria-labelledby="v-pills-monitoreo-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-chart-line fa-fw me-2"></i> Monitoreo y Control
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Monitoreo y Control - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: MONITOREO Y CONTROL ?>

					<?php #region region TAB: CIERRE ?>
					<div class="tab-pane fade" id="v-pills-cierre" role="tabpanel"
						 aria-labelledby="v-pills-cierre-tab">
						<div class="panel panel-inverse no-border-radious">
							<div class="panel-heading no-border-radious">
								<h4 class="panel-title">
									<i class="fa fa-flag-checkered fa-fw me-2"></i> Cierre
								</h4>
								<div class="panel-heading-btn">
									<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand">
										<i class="fa fa-expand"></i>
									</a>
									<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse">
										<i class="fa fa-minus"></i>
									</a>
								</div>
							</div>
							<div class="panel-body">
								<div class="text-center">
									<p class="text-muted">Contenido del tab Cierre - Por implementar</p>
								</div>
							</div>
						</div>
					</div>
					<?php #endregion TAB: CIERRE ?>
					
				</div>
			</div>
		</div>
		<?php #endregion PROJECT MANAGEMENT CONTENT ?>
		<?php endif; ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top">
		<i class="fa fa-angle-up"></i>
	</a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>

<script>
document.addEventListener('DOMContentLoaded', function() {
	// Initialize any JavaScript functionality here
	console.log('Project Management page loaded successfully');

	// Handle tab persistence after page reload (with small delay to ensure DOM is ready)
	setTimeout(function() {
		handleTabPersistence();
	}, 100);

	// Load documents tables
	loadContratosTable();
	loadPolizasTable();

	// File upload validation
	const documentUploadForms = document.querySelectorAll('.document-upload-form');

	documentUploadForms.forEach(function(form) {
		form.addEventListener('submit', function(event) {
			const fileInput = form.querySelector('input[type="file"]');
			const submitBtn = form.querySelector('button[type="submit"]');

			// Reset validation styling
			fileInput.classList.remove('is-invalid');

			// Check if a file is selected
			if (!fileInput.files || fileInput.files.length === 0) {
				event.preventDefault();
				fileInput.classList.add('is-invalid');

				// Show SweetAlert error
				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'Debe seleccionar un archivo PDF para subir.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('Debe seleccionar un archivo PDF para subir.');
				}
				return false;
			}

			const file = fileInput.files[0];

			// Validate file type
			if (file.type !== 'application/pdf') {
				event.preventDefault();
				fileInput.classList.add('is-invalid');

				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'Solo se permiten archivos PDF. Por favor, seleccione un archivo PDF válido.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('Solo se permiten archivos PDF. Por favor, seleccione un archivo PDF válido.');
				}
				return false;
			}

			// Validate file size (max 50MB)
			const maxSize = 50 * 1024 * 1024; // 50MB in bytes
			if (file.size > maxSize) {
				event.preventDefault();
				fileInput.classList.add('is-invalid');
				
				if (typeof Swal !== 'undefined') {
					Swal.fire({
						icon: 'error',
						title: 'Error',
						text: 'El archivo es demasiado grande. El tamaño máximo permitido es 50MB.',
						confirmButtonColor: '#d33'
					});
				} else {
					alert('El archivo es demasiado grande. El tamaño máximo permitido es 50MB.');
				}
				return false;
			}

			// If all validations pass, show loading indicator
			const originalText = submitBtn.innerHTML;
			submitBtn.innerHTML = '<i class="fa fa-spinner fa-spin me-1"></i>Subiendo...';
			submitBtn.disabled = true;

			// Re-enable button after a timeout in case of server error
			setTimeout(function() {
				submitBtn.innerHTML = originalText;
				submitBtn.disabled = false;
			}, 30000); // 30 seconds timeout

			return true;
		});
	});

	// Load Contratos Table
	function loadContratosTable() {
		const tableBody = document.getElementById('contratos-table-body');
		if (!tableBody) return;

		// Clear existing content
		tableBody.innerHTML = '';

		<?php if (!empty($contratos)): ?>
			<?php foreach ($contratos as $contrato): ?>
				const contratoRow = document.createElement('tr');
				contratoRow.innerHTML = `
					<td class="align-middle">
						<i class="fa fa-file-pdf text-danger me-2"></i>
						<?php echo htmlspecialchars($contrato->getArchivo()); ?>
					</td>
					<td class="align-middle"><?php echo htmlspecialchars($contrato->getFechaCreacionFormateada() ?? 'N/A'); ?></td>
					<td class="align-middle">
						<div class="btn-group" role="group">
							<a href="../resources/uploads/proyectos/<?php echo $contrato->getIdProyecto(); ?>/planificacion/<?php echo htmlspecialchars($contrato->getArchivo()); ?>"
							   target="_blank" class="btn btn-xs btn-outline-primary"
							   title="Descargar">
								<i class="fa fa-download"></i>
							</a>
							<button type="button" class="btn btn-xs btn-outline-danger btn-delete-contrato"
									data-contrato-id="<?php echo $contrato->getId(); ?>"
									data-archivo-nombre="<?php echo htmlspecialchars($contrato->getArchivo()); ?>"
									title="Eliminar">
								<i class="fa fa-trash-alt"></i>
							</button>
						</div>
					</td>
				`;
				tableBody.appendChild(contratoRow);
			<?php endforeach; ?>
		<?php else: ?>
			tableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No hay contratos subidos</td></tr>';
		<?php endif; ?>

		// Add event listeners for delete buttons
		document.querySelectorAll('.btn-delete-contrato').forEach(btn => {
			btn.addEventListener('click', function() {
				const contratoId = this.dataset.contratoId;
				const archivoNombre = this.dataset.archivoNombre;
				deleteContrato(contratoId, archivoNombre);
			});
		});
	}

	// Load Polizas Table
	function loadPolizasTable() {
		const tableBody = document.getElementById('polizas-table-body');
		if (!tableBody) return;

		// Clear existing content
		tableBody.innerHTML = '';

		<?php if (!empty($polizas)): ?>
			<?php foreach ($polizas as $poliza): ?>
				const polizaRow = document.createElement('tr');
				polizaRow.innerHTML = `
					<td class="align-middle">
						<i class="fa fa-file-pdf text-danger me-2"></i>
						<?php echo htmlspecialchars($poliza->getArchivo()); ?>
					</td>
					<td class="align-middle"><?php echo htmlspecialchars($poliza->getFechaCreacionFormateada() ?? 'N/A'); ?></td>
					<td class="align-middle">
						<div class="btn-group" role="group">
							<a href="../resources/uploads/proyectos/<?php echo $poliza->getIdProyecto(); ?>/planificacion/<?php echo htmlspecialchars($poliza->getArchivo()); ?>"
							   target="_blank" class="btn btn-xs btn-outline-primary"
							   title="Descargar">
								<i class="fa fa-download"></i>
							</a>
							<button type="button" class="btn btn-xs btn-outline-danger btn-delete-poliza"
									data-poliza-id="<?php echo $poliza->getId(); ?>"
									data-archivo-nombre="<?php echo htmlspecialchars($poliza->getArchivo()); ?>"
									title="Eliminar">
								<i class="fa fa-trash-alt"></i>
							</button>
						</div>
					</td>
				`;
				tableBody.appendChild(polizaRow);
			<?php endforeach; ?>
		<?php else: ?>
			tableBody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No hay pólizas subidas</td></tr>';
		<?php endif; ?>

		// Add event listeners for delete buttons
		document.querySelectorAll('.btn-delete-poliza').forEach(btn => {
			btn.addEventListener('click', function() {
				const polizaId = this.dataset.polizaId;
				const archivoNombre = this.dataset.archivoNombre;
				deletePoliza(polizaId, archivoNombre);
			});
		});
	}

	// Delete Contrato Function
	function deleteContrato(contratoId, archivoNombre) {
		if (typeof Swal !== 'undefined') {
			Swal.fire({
				title: '¿Está seguro?',
				text: `¿Desea eliminar el contrato "${archivoNombre}"? Esta acción no se puede deshacer.`,
				icon: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#d33',
				cancelButtonColor: '#3085d6',
				confirmButtonText: 'Sí, eliminar',
				cancelButtonText: 'Cancelar'
			}).then((result) => {
				if (result.isConfirmed) {
					performDeleteContrato(contratoId);
				}
			});
		} else {
			if (confirm(`¿Está seguro de que desea eliminar el contrato "${archivoNombre}"?`)) {
				performDeleteContrato(contratoId);
			}
		}
	}

	// Perform Delete Contrato
	function performDeleteContrato(contratoId) {
		const formData = new FormData();
		formData.append('action', 'delete_contrato');
		formData.append('contrato_id', contratoId);

		fetch('gestion-proyecto', {
			method: 'POST',
			body: formData
		})
		.then(response => response.json())
		.then(data => {
			if (data.success) {
				if (typeof Swal !== 'undefined') {
					Swal.fire('Eliminado', data.message, 'success');
				} else {
					alert(data.message);
				}
				// Reload the table
				loadContratosTable();
			} else {
				if (typeof Swal !== 'undefined') {
					Swal.fire('Error', data.message, 'error');
				} else {
					alert('Error: ' + data.message);
				}
			}
		})
		.catch(error => {
			console.error('Error:', error);
			if (typeof Swal !== 'undefined') {
				Swal.fire('Error', 'Error de conexión al eliminar el contrato', 'error');
			} else {
				alert('Error de conexión al eliminar el contrato');
			}
		});
	}

	// Delete Poliza Function
	function deletePoliza(polizaId, archivoNombre) {
		if (typeof Swal !== 'undefined') {
			Swal.fire({
				title: '¿Está seguro?',
				text: `¿Desea eliminar la póliza "${archivoNombre}"? Esta acción no se puede deshacer.`,
				icon: 'warning',
				showCancelButton: true,
				confirmButtonColor: '#d33',
				cancelButtonColor: '#3085d6',
				confirmButtonText: 'Sí, eliminar',
				cancelButtonText: 'Cancelar'
			}).then((result) => {
				if (result.isConfirmed) {
					performDeletePoliza(polizaId);
				}
			});
		} else {
			if (confirm(`¿Está seguro de que desea eliminar la póliza "${archivoNombre}"?`)) {
				performDeletePoliza(polizaId);
			}
		}
	}

	// Perform Delete Poliza
	function performDeletePoliza(polizaId) {
		const formData = new FormData();
		formData.append('action', 'delete_poliza');
		formData.append('poliza_id', polizaId);

		fetch('gestion-proyecto', {
			method: 'POST',
			body: formData
		})
		.then(response => response.json())
		.then(data => {
			if (data.success) {
				if (typeof Swal !== 'undefined') {
					Swal.fire('Eliminado', data.message, 'success');
				} else {
					alert(data.message);
				}
				// Reload the table
				loadPolizasTable();
			} else {
				if (typeof Swal !== 'undefined') {
					Swal.fire('Error', data.message, 'error');
				} else {
					alert('Error: ' + data.message);
				}
			}
		})
		.catch(error => {
			console.error('Error:', error);
			if (typeof Swal !== 'undefined') {
				Swal.fire('Error', 'Error de conexión al eliminar la póliza', 'error');
			} else {
				alert('Error de conexión al eliminar la póliza');
			}
		});
	}

	// Handle tab persistence after page reload
	function handleTabPersistence() {
		// Check URL parameters for active tab
		const urlParams = new URLSearchParams(window.location.search);
		const activeTab = urlParams.get('tab');

		console.log('Current URL:', window.location.href);
		console.log('URL parameters:', window.location.search);
		console.log('Active tab parameter:', activeTab);

		// Default tab if no parameter or invalid tab
		const defaultTab = 'v-pills-informacion';
		let targetTabId = defaultTab;

		// Validate the tab parameter exists in the DOM
		if (activeTab) {
			const targetTab = document.getElementById(activeTab + '-tab');
			const targetPane = document.getElementById(activeTab);

			if (targetTab && targetPane) {
				targetTabId = activeTab;
			} else {
				console.warn('Invalid tab parameter:', activeTab, 'falling back to default tab');
			}
		}

		// Always ensure we have an active tab
		// Remove active class from all tabs and content first
		document.querySelectorAll('.nav-link').forEach(tab => {
			tab.classList.remove('active');
			tab.setAttribute('aria-selected', 'false');
		});
		document.querySelectorAll('.tab-pane').forEach(pane => {
			pane.classList.remove('show', 'active');
		});

		// Activate the target tab (either from URL parameter or default)
		const finalTab = document.getElementById(targetTabId + '-tab');
		const finalPane = document.getElementById(targetTabId);

		if (finalTab && finalPane) {
			finalTab.classList.add('active');
			finalTab.setAttribute('aria-selected', 'true');
			finalPane.classList.add('show', 'active');

			// Try to trigger Bootstrap tab event for proper initialization
			try {
				if (typeof bootstrap !== 'undefined' && bootstrap.Tab) {
					const tabTrigger = new bootstrap.Tab(finalTab);
					// Don't call show() as it might cause issues, just ensure classes are set
				}
			} catch (e) {
				console.warn('Bootstrap Tab initialization failed:', e);
			}

			console.log('Activated tab:', targetTabId);
		} else {
			console.error('Failed to activate any tab, DOM elements not found for:', targetTabId);

			// Emergency fallback - try to activate the default informacion tab
			const emergencyTab = document.getElementById('v-pills-informacion-tab');
			const emergencyPane = document.getElementById('v-pills-informacion');

			if (emergencyTab && emergencyPane) {
				emergencyTab.classList.add('active');
				emergencyTab.setAttribute('aria-selected', 'true');
				emergencyPane.classList.add('show', 'active');
				console.log('Emergency fallback: activated informacion tab');
			}
		}

		// Add event listeners to track tab changes
		document.querySelectorAll('.nav-link').forEach(tab => {
			tab.addEventListener('click', function() {
				const tabId = this.getAttribute('data-bs-target');
				if (tabId) {
					// Store current tab in localStorage for form submissions
					localStorage.setItem('currentActiveTab', tabId);

					// Update all hidden active_tab inputs immediately
					document.querySelectorAll('.dynamic-tab-input').forEach(input => {
						input.value = tabId;
					});
				}
			});
		});

		// Update form active_tab values when tabs change (Bootstrap event)
		document.querySelectorAll('.nav-link').forEach(tab => {
			tab.addEventListener('shown.bs.tab', function() {
				const tabId = this.getAttribute('data-bs-target');
				if (tabId) {
					// Update all hidden active_tab inputs
					document.querySelectorAll('.dynamic-tab-input').forEach(input => {
						input.value = tabId;
					});
					console.log('Tab changed to:', tabId);
				}
			});
		});

		// Initialize form inputs with current active tab
		const currentActiveTab = document.querySelector('.nav-link.active');
		if (currentActiveTab) {
			const currentTabId = currentActiveTab.getAttribute('data-bs-target');
			if (currentTabId) {
				document.querySelectorAll('.dynamic-tab-input').forEach(input => {
					input.value = currentTabId;
				});
				console.log('Initialized form inputs with active tab:', currentTabId);
			}
		} else {
			console.warn('No active tab found during initialization');
		}
	}

	// Debug function to test tab switching (can be called from browser console)
	window.testTabSwitch = function(tabId) {
		console.log('Testing tab switch to:', tabId);
		const tab = document.getElementById(tabId + '-tab');
		if (tab) {
			tab.click();
		} else {
			console.error('Tab not found:', tabId);
		}
	};

	// Debug function to check current tab state
	window.checkTabState = function() {
		const activeTab = document.querySelector('.nav-link.active');
		const activePane = document.querySelector('.tab-pane.active');
		const formInputs = document.querySelectorAll('.dynamic-tab-input');

		console.log('Active tab:', activeTab ? activeTab.id : 'none');
		console.log('Active pane:', activePane ? activePane.id : 'none');
		console.log('Form input values:');
		formInputs.forEach((input, index) => {
			console.log(`  Input ${index}:`, input.value);
		});
	};
});
</script>
<?php #endregion JS ?>

</body>
</html>
